<?php
/**
 * 演示页面 - 展示更多命令注入漏洞场景
 * 功能：提供额外的漏洞演示和测试接口
 */

// 引入其他文件
require_once 'functions.php';
require_once 'utils.php';

// 处理不同的演示请求
if (isset($_GET['demo'])) {
    $demo_type = $_GET['demo'];
    
    echo "<h2>命令注入漏洞演示</h2>";
    
    switch($demo_type) {
        case 'system_info':
            if (isset($_GET['info_type'])) {
                echo "<h3>系统信息查询演示</h3>";
                echo "<p>输入的信息类型: " . htmlspecialchars($_GET['info_type']) . "</p>";
                echo "<pre>";
                get_system_info($_GET['info_type']);
                echo "</pre>";
            }
            break;
            
        case 'log_search':
            if (isset($_GET['keyword'])) {
                echo "<h3>日志搜索演示</h3>";
                echo "<p>搜索关键词: " . htmlspecialchars($_GET['keyword']) . "</p>";
                echo "<pre>";
                search_logs($_GET['keyword']);
                echo "</pre>";
            }
            break;
            
        case 'system_check':
            if (isset($_GET['check_type'])) {
                echo "<h3>系统状态检查演示</h3>";
                echo "<p>检查类型: " . htmlspecialchars($_GET['check_type']) . "</p>";
                echo "<pre>";
                check_system_status($_GET['check_type']);
                echo "</pre>";
            }
            break;
            
        case 'log_action':
            if (isset($_GET['action']) && isset($_GET['details'])) {
                echo "<h3>用户操作日志演示</h3>";
                echo "<p>操作: " . htmlspecialchars($_GET['action']) . "</p>";
                echo "<p>详情: " . htmlspecialchars($_GET['details']) . "</p>";
                echo "<pre>";
                log_user_action($_GET['action'], $_GET['details']);
                echo "</pre>";
            }
            break;
            
        default:
            echo "<p>未知的演示类型</p>";
            break;
    }
    
    echo "<hr>";
    show_help();
    
} else {
    // 显示演示选项
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>命令注入漏洞演示</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .demo-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
            .demo-link { display: inline-block; margin: 10px; padding: 10px 15px; background: #007cba; color: white; text-decoration: none; }
            .demo-link:hover { background: #005a8b; }
            .warning { background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>命令注入漏洞演示页面</h1>
        
        <div class="warning">
            <strong>警告：</strong> 这是一个用于安全教育和测试的演示项目，包含故意设计的安全漏洞。请勿在生产环境中使用！
        </div>
        
        <div class="demo-section">
            <h3>1. 系统信息查询演示</h3>
            <p>测试 get_system_info() 函数的命令注入漏洞</p>
            <a href="?demo=system_info&info_type=date" class="demo-link">查看日期</a>
            <a href="?demo=system_info&info_type=uptime" class="demo-link">查看运行时间</a>
            <a href="?demo=system_info&info_type=whoami" class="demo-link">执行whoami</a>
            <a href="?demo=system_info&info_type=ls -la" class="demo-link">列出文件</a>
        </div>
        
        <div class="demo-section">
            <h3>2. 日志搜索演示</h3>
            <p>测试 search_logs() 函数的命令注入漏洞</p>
            <a href="?demo=log_search&keyword=error" class="demo-link">搜索error</a>
            <a href="?demo=log_search&keyword=test'; whoami; echo '" class="demo-link">注入whoami命令</a>
            <a href="?demo=log_search&keyword=test'; ls -la; echo '" class="demo-link">注入ls命令</a>
        </div>
        
        <div class="demo-section">
            <h3>3. 系统状态检查演示</h3>
            <p>测试 check_system_status() 函数的命令注入漏洞</p>
            <a href="?demo=system_check&check_type=disk" class="demo-link">检查磁盘</a>
            <a href="?demo=system_check&check_type=memory" class="demo-link">检查内存</a>
            <a href="?demo=system_check&check_type=id" class="demo-link">执行id命令</a>
            <a href="?demo=system_check&check_type=cat /etc/passwd" class="demo-link">查看passwd文件</a>
        </div>
        
        <div class="demo-section">
            <h3>4. 用户操作日志演示</h3>
            <p>测试 log_user_action() 函数的命令注入漏洞</p>
            <a href="?demo=log_action&action=login&details=user123" class="demo-link">正常日志</a>
            <a href="?demo=log_action&action=test&details=normal'; whoami; echo 'done" class="demo-link">注入whoami</a>
            <a href="?demo=log_action&action=test&details=normal'; cat /etc/hosts; echo 'done" class="demo-link">注入cat命令</a>
        </div>
        
        <div class="demo-section">
            <h3>返回主页</h3>
            <a href="index.php" class="demo-link">返回主页面</a>
        </div>
        
        <?php show_help(); ?>
        
    </body>
    </html>
    <?php
}
?>
