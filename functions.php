<?php
/**
 * 核心功能函数 - 包含存在命令注入漏洞的函数
 * 功能：提供系统查询相关的核心函数
 */

/**
 * 检查主机名连通性 - 存在命令注入漏洞的函数
 * 漏洞点：直接将用户输入拼接到系统命令中，未进行任何过滤
 * 
 * @param string $hostname 用户输入的主机名
 */
function check_hostname($hostname) {
    // 漏洞代码：直接将用户输入拼接到ping命令中
    // 攻击者可以通过输入 "google.com; ls -la" 来执行额外的命令
    $command = "ping -c 3 " . $hostname;
    
    echo "执行命令: " . $command . "\n";
    echo "结果:\n";
    
    // 使用system()函数执行命令 - 这是漏洞的触发点
    system($command);
}

/**
 * 查看文件内容 - 存在命令注入漏洞的函数
 * 漏洞点：直接将用户输入拼接到系统命令中
 * 
 * @param string $filepath 用户输入的文件路径
 */
function view_file($filepath) {
    // 漏洞代码：直接将用户输入拼接到cat命令中
    // 攻击者可以通过输入 "/etc/passwd; whoami" 来执行额外的命令
    $command = "cat " . $filepath;
    
    echo "执行命令: " . $command . "\n";
    echo "结果:\n";
    
    // 使用system()函数执行命令 - 这是漏洞的触发点
    system($command);
}

/**
 * 获取系统信息 - 另一个存在漏洞的函数
 * 漏洞点：通过参数传递用户输入到系统命令
 * 
 * @param string $info_type 信息类型
 */
function get_system_info($info_type) {
    // 根据不同的信息类型执行不同的命令
    switch($info_type) {
        case 'date':
            $command = "date";
            break;
        case 'uptime':
            $command = "uptime";
            break;
        case 'users':
            $command = "who";
            break;
        default:
            // 漏洞：直接使用用户输入作为命令
            // 攻击者可以传入任意命令
            $command = $info_type;
            break;
    }
    
    echo "执行命令: " . $command . "\n";
    echo "结果:\n";
    
    // 使用system()函数执行命令
    system($command);
}

/**
 * 搜索日志文件 - 存在命令注入漏洞的函数
 * 漏洞点：将用户输入直接用于grep命令
 * 
 * @param string $keyword 搜索关键词
 * @param string $logfile 日志文件路径
 */
function search_logs($keyword, $logfile = "/var/log/syslog") {
    // 漏洞代码：直接将用户输入拼接到grep命令中
    // 攻击者可以通过输入特殊字符来注入命令
    $command = "grep '" . $keyword . "' " . $logfile;
    
    echo "执行命令: " . $command . "\n";
    echo "结果:\n";
    
    // 使用system()函数执行命令
    system($command);
}

?>
