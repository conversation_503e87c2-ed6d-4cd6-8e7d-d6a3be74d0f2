<?php
/**
 * 主页面 - 命令注入漏洞演示
 * 这个文件包含存在漏洞的ping功能和文件查看功能
 */

// 包含工具函数文件
require_once 'utils.php';
require_once 'secure.php';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络工具 - 命令注入演示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .tool-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .vulnerable { border-color: #ff6b6b; background-color: #ffe6e6; }
        .secure { border-color: #51cf66; background-color: #e6ffe6; }
        input[type="text"] { width: 300px; padding: 5px; }
        button { padding: 8px 15px; margin: 5px; }
        .result { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
        .warning { color: #d63031; font-weight: bold; }
        .safe { color: #00b894; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>网络工具演示</h1>
        <p><strong>警告：</strong>这是一个安全漏洞演示项目，仅用于教育目的！</p>

        <!-- 存在漏洞的Ping工具 -->
        <div class="tool-section vulnerable">
            <h2><span class="warning">🚨 漏洞版本</span> - Ping工具</h2>
            <p>这个工具存在命令注入漏洞，可以执行任意系统命令。</p>
            <form method="GET" action="">
                <input type="text" name="host" placeholder="输入要ping的主机 (例如: google.com)" 
                       value="<?php echo isset($_GET['host']) ? htmlspecialchars($_GET['host']) : ''; ?>">
                <button type="submit" name="action" value="ping_vulnerable">执行Ping (漏洞版)</button>
            </form>
            
            <?php
            if (isset($_GET['action']) && $_GET['action'] === 'ping_vulnerable' && isset($_GET['host'])) {
                echo '<div class="result">';
                echo '<h3>执行结果：</h3>';
                echo '<pre>';
                // 调用存在漏洞的ping函数
                ping_host_vulnerable($_GET['host']);
                echo '</pre>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- 存在漏洞的文件查看工具 -->
        <div class="tool-section vulnerable">
            <h2><span class="warning">🚨 漏洞版本</span> - 文件查看工具</h2>
            <p>这个工具也存在命令注入漏洞，通过ls命令查看文件。</p>
            <form method="GET" action="">
                <input type="text" name="directory" placeholder="输入目录路径 (例如: /tmp)" 
                       value="<?php echo isset($_GET['directory']) ? htmlspecialchars($_GET['directory']) : ''; ?>">
                <button type="submit" name="action" value="list_vulnerable">查看目录 (漏洞版)</button>
            </form>
            
            <?php
            if (isset($_GET['action']) && $_GET['action'] === 'list_vulnerable' && isset($_GET['directory'])) {
                echo '<div class="result">';
                echo '<h3>目录内容：</h3>';
                echo '<pre>';
                // 调用存在漏洞的目录列表函数
                list_directory_vulnerable($_GET['directory']);
                echo '</pre>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- 安全版本的工具 -->
        <div class="tool-section secure">
            <h2><span class="safe">✅ 安全版本</span> - Ping工具</h2>
            <p>这个版本对输入进行了适当的验证和过滤。</p>
            <form method="GET" action="">
                <input type="text" name="safe_host" placeholder="输入要ping的主机 (例如: google.com)" 
                       value="<?php echo isset($_GET['safe_host']) ? htmlspecialchars($_GET['safe_host']) : ''; ?>">
                <button type="submit" name="action" value="ping_safe">执行Ping (安全版)</button>
            </form>
            
            <?php
            if (isset($_GET['action']) && $_GET['action'] === 'ping_safe' && isset($_GET['safe_host'])) {
                echo '<div class="result">';
                echo '<h3>执行结果：</h3>';
                echo '<pre>';
                // 调用安全的ping函数
                ping_host_secure($_GET['safe_host']);
                echo '</pre>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- 利用示例说明 -->
        <div class="tool-section">
            <h2>🎯 漏洞利用示例</h2>
            <p><strong>命令注入攻击示例：</strong></p>
            <ul>
                <li><code>?host=google.com;whoami&action=ping_vulnerable</code> - 执行whoami命令</li>
                <li><code>?host=google.com;ls -la&action=ping_vulnerable</code> - 列出当前目录文件</li>
                <li><code>?host=google.com;cat /etc/passwd&action=ping_vulnerable</code> - 读取系统文件</li>
                <li><code>?directory=/tmp;whoami&action=list_vulnerable</code> - 在目录查看中注入命令</li>
            </ul>
            <p><strong>注意：</strong>在实际环境中，这些攻击可能导致严重的安全问题！</p>
        </div>
    </div>
</body>
</html>
