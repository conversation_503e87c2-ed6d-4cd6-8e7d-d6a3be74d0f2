<?php
/**
 * 主页面 - 命令注入漏洞演示项目
 * 功能：提供用户界面，接收用户输入并调用处理函数
 */

// 引入其他文件
require_once 'functions.php';
require_once 'utils.php';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统信息查询工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 300px; padding: 8px; border: 1px solid #ccc; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 15px; background: #f5f5f5; border: 1px solid #ddd; }
        .warning { color: #d63384; font-size: 12px; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>系统信息查询工具</h1>
        <p>这是一个简单的系统信息查询工具，可以帮助您获取服务器的基本信息。</p>
        
        <form method="GET" action="">
            <div class="form-group">
                <label for="hostname">主机名查询：</label>
                <input type="text" id="hostname" name="hostname" placeholder="输入要查询的主机名" 
                       value="<?php echo isset($_GET['hostname']) ? htmlspecialchars($_GET['hostname']) : ''; ?>">
                <div class="warning">示例：localhost 或 google.com</div>
            </div>
            
            <div class="form-group">
                <label for="file">文件查看：</label>
                <input type="text" id="file" name="file" placeholder="输入要查看的文件路径" 
                       value="<?php echo isset($_GET['file']) ? htmlspecialchars($_GET['file']) : ''; ?>">
                <div class="warning">示例：/etc/passwd 或 /etc/hosts</div>
            </div>
            
            <button type="submit">查询</button>
        </form>

        <?php
        // 处理用户输入
        if (isset($_GET['hostname']) && !empty($_GET['hostname'])) {
            echo "<div class='result'>";
            echo "<h3>主机名查询结果：</h3>";
            echo "<pre>";
            // 调用存在漏洞的函数
            check_hostname($_GET['hostname']);
            echo "</pre>";
            echo "</div>";
        }

        if (isset($_GET['file']) && !empty($_GET['file'])) {
            echo "<div class='result'>";
            echo "<h3>文件内容：</h3>";
            echo "<pre>";
            // 调用存在漏洞的函数
            view_file($_GET['file']);
            echo "</pre>";
            echo "</div>";
        }
        ?>

        <div style="margin-top: 40px; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h4>使用说明：</h4>
            <ul>
                <li>主机名查询：输入域名或IP地址，系统将使用ping命令检查连通性</li>
                <li>文件查看：输入文件路径，系统将显示文件内容</li>
                <li>支持的文件格式：文本文件（.txt, .log, .conf等）</li>
            </ul>
        </div>
    </div>
</body>
</html>
