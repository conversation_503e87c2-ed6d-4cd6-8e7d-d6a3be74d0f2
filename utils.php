<?php
/**
 * 工具函数文件 - 提供辅助功能
 * 功能：提供日志记录、输入处理等辅助函数
 */

/**
 * 记录用户操作日志 - 存在命令注入漏洞的函数
 * 漏洞点：将用户输入直接写入到系统命令中
 * 
 * @param string $action 用户操作
 * @param string $details 操作详情
 */
function log_user_action($action, $details) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] Action: $action, Details: $details";
    
    // 漏洞代码：直接将用户输入拼接到echo命令中写入日志文件
    // 攻击者可以通过输入特殊字符来注入命令
    $command = "echo '$log_entry' >> /tmp/user_actions.log";
    
    echo "记录日志: " . $command . "\n";
    
    // 使用system()函数执行命令
    system($command);
}

/**
 * 显示帮助信息
 * 安全函数：不涉及用户输入
 */
function show_help() {
    echo "<div style='background: #e7f3ff; padding: 15px; margin: 20px 0; border-left: 4px solid #2196F3;'>";
    echo "<h4>漏洞利用示例：</h4>";
    echo "<ul>";
    echo "<li><strong>命令注入示例1：</strong> 在主机名输入框中输入：<code>google.com; whoami</code></li>";
    echo "<li><strong>命令注入示例2：</strong> 在文件路径输入框中输入：<code>/etc/passwd; ls -la</code></li>";
    echo "<li><strong>命令注入示例3：</strong> 在主机名输入框中输入：<code>localhost && cat /etc/hosts</code></li>";
    echo "<li><strong>命令注入示例4：</strong> 在文件路径输入框中输入：<code>/dev/null; id</code></li>";
    echo "</ul>";
    echo "<p><strong>注意：</strong> 这些示例仅用于安全测试和教育目的。</p>";
    echo "</div>";
}

/**
 * 格式化输出结果
 * 相对安全的函数，但仍可能被利用
 * 
 * @param string $title 标题
 * @param string $content 内容
 */
function format_output($title, $content) {
    echo "<h4>" . htmlspecialchars($title) . "</h4>";
    echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
    echo "<pre>" . $content . "</pre>";
    echo "</div>";
}

/**
 * 检查系统状态 - 存在命令注入漏洞的函数
 * 漏洞点：根据用户输入执行不同的系统命令
 * 
 * @param string $check_type 检查类型
 */
function check_system_status($check_type) {
    // 根据用户输入决定执行哪个命令
    switch($check_type) {
        case 'disk':
            $command = "df -h";
            break;
        case 'memory':
            $command = "free -h";
            break;
        case 'process':
            $command = "ps aux | head -10";
            break;
        case 'network':
            $command = "netstat -tuln | head -10";
            break;
        default:
            // 漏洞：如果输入不匹配预定义选项，直接执行用户输入
            $command = $check_type;
            break;
    }
    
    echo "执行系统检查: " . $command . "\n";
    echo "结果:\n";
    
    // 使用system()函数执行命令
    system($command);
}

?>
